defmodule QuoteX.Repo.Migrations.MigrateResources2 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:properties) do
      modify :name, :text, null: false
    end

    create unique_index(:properties, [:name], name: "properties_unique_name_index")
  end

  def down do
    drop_if_exists unique_index(:properties, [:name], name: "properties_unique_name_index")

    alter table(:properties) do
      modify :name, :text, null: true
    end
  end
end
