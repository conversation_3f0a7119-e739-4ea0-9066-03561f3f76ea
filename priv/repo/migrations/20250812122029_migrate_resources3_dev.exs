defmodule QuoteX.Repo.Migrations.MigrateResources3 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    execute("CREATE EXTENSION IF NOT EXISTS \"citext\"")

    create table(:operators, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :email, :citext, null: false
    end

    create unique_index(:operators, [:email], name: "operators_unique_email_index")
  end

  def down do
    drop_if_exists unique_index(:operators, [:email], name: "operators_unique_email_index")

    drop table(:operators)
  end
end
