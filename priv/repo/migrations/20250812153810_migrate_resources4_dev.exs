defmodule QuoteX.Repo.Migrations.MigrateResources4 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:properties_versions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :version_action_type, :text, null: false
      add :version_action_name, :text, null: false

      add :version_source_id,
          references(:properties,
            column: :id,
            name: "properties_versions_version_source_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :changes, :map

      add :version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    alter table(:properties) do
      add :archived_at, :utc_datetime_usec
    end
  end

  def down do
    alter table(:properties) do
      remove :archived_at
    end

    drop constraint(:properties_versions, "properties_versions_version_source_id_fkey")

    drop table(:properties_versions)
  end
end
