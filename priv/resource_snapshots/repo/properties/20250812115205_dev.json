{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "3093D2A037D93473FCBD04B79205BC7259613E8BF5E36674CB72654768CE4986", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "properties_unique_name_index", "keys": [{"type": "atom", "value": "name"}], "name": "unique_name", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.QuoteX.Repo", "schema": null, "table": "properties"}