defmodule QuoteX.Accounts do
  @moduledoc """
  The Accounts domain is responsible for managing operators and properties.
  """
  use Ash.Domain,
    otp_app: :quotex,
    extensions: [AshAdmin.Domain, AshPaperTrail.Domain]

  admin do
    show? true
  end

  paper_trail do
    include_versions? true
  end

  resources do
    resource QuoteX.Accounts.Property
    resource QuoteX.Accounts.Property.Version
  end
end
