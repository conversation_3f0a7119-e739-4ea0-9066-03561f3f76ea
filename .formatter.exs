[
  import_deps: [
    :ash_postgres,
    :ash_admin,
    :ash_phoenix,
    :ash_authentication_phoenix,
    :ash_authentication,
    :ash,
    :reactor,
    :ecto,
    :ecto_sql,
    :phoenix,
    :ash_archival,
    :ash_paper_trail
  ],
  subdirectories: ["priv/*/migrations"],
  plugins: [Spark.Formatter, Phoenix.LiveView.HTMLFormatter],
  inputs: ["*.{heex,ex,exs}", "{config,lib,test}/**/*.{heex,ex,exs}", "priv/*/seeds.exs"]
]
